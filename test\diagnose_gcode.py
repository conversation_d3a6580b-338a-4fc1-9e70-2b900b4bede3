#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
G-code位置诊断工具
用于分析G-code文件中的位置是否在机器人工作空间内
"""

import sys
import os
import math

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

# 导入run_gcode模块的函数
sys.path.append(os.path.dirname(__file__))
from run_gcode import (
    parse_gcode_file, 
    analyze_gcode_workspace,
    convert_abc_to_rxryrz,
    validate_target_position,
    GCODE_FILE,
    DEFAULT_RX, DEFAULT_RY, DEFAULT_RZ
)

def diagnose_gcode_positions():
    """诊断G-code文件中的位置"""
    print("=" * 60)
    print("🔍 G-code位置诊断工具")
    print("=" * 60)
    
    # 解析G-code文件
    print(f"📄 分析文件: {GCODE_FILE}")
    gcode_path = parse_gcode_file(GCODE_FILE)
    
    if not gcode_path:
        print("❌ 无法解析G-code文件")
        return
    
    # 分析工作空间
    analyze_gcode_workspace(gcode_path)
    
    # 检查每个点的安全性
    print("\n" + "=" * 60)
    print("🛡️ 逐点安全检查")
    print("=" * 60)
    
    default_orientation = [DEFAULT_RX, DEFAULT_RY, DEFAULT_RZ]
    problem_points = []
    
    for i, point in enumerate(gcode_path):
        # 计算目标位置和姿态
        target_orientation = convert_abc_to_rxryrz(point, default_orientation)
        target_pos = [point['x'], point['y'], point['z'], 
                     target_orientation[0], target_orientation[1], target_orientation[2]]
        
        print(f"\n点{i+1}: X={point['x']:.3f}, Y={point['y']:.3f}, Z={point['z']:.3f}", end="")
        if point['has_abc']:
            print(f", A={point['a']:.3f}, B={point['b']:.3f}, C={point['c']:.3f}")
        else:
            print()
        
        # 安全检查
        is_safe = validate_target_position(target_pos, i)
        if not is_safe:
            problem_points.append(i+1)
            print(f"  ❌ 点{i+1}存在问题")
        else:
            print(f"  ✅ 点{i+1}检查通过")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 诊断总结")
    print("=" * 60)
    
    if problem_points:
        print(f"❌ 发现 {len(problem_points)} 个问题点: {problem_points}")
        print("\n💡 解决建议:")
        print("1. 检查G-code文件中的坐标是否正确")
        print("2. 调整工作空间限制参数 WORKSPACE_LIMITS")
        print("3. 检查机器人当前位置是否合适")
        print("4. 考虑缩放或平移G-code坐标")
    else:
        print("✅ 所有点都通过了安全检查")
        print("💡 如果仍然报告位置不可达，可能的原因:")
        print("1. 机器人当前姿态导致逆运动学无解")
        print("2. 关节角度限制")
        print("3. 奇异点附近")
        print("4. 机器人标定问题")
    
    print("\n🔧 调试建议:")
    print("1. 启用干运行模式 (DRY_RUN_MODE = True) 测试")
    print("2. 降低运动速度")
    print("3. 检查用户坐标系设置")
    print("4. 尝试手动示教几个关键点")

def suggest_workspace_settings():
    """建议工作空间设置"""
    print("\n" + "=" * 60)
    print("⚙️ 工作空间设置建议")
    print("=" * 60)
    
    print("根据INEXBOT机器人的典型工作空间，建议设置:")
    print("""
WORKSPACE_LIMITS = {
    'x_min': -600.0, 'x_max': 600.0,    # X轴范围 (mm)
    'y_min': -600.0, 'y_max': 600.0,    # Y轴范围 (mm)  
    'z_min': -100.0, 'z_max': 800.0,    # Z轴范围 (mm)
    'rx_min': -180.0, 'rx_max': 180.0,  # RX角度范围 (度)
    'ry_min': -180.0, 'ry_max': 180.0,  # RY角度范围 (度)
    'rz_min': -180.0, 'rz_max': 180.0,  # RZ角度范围 (度)
}
    """)
    
    print("⚠️ 注意:")
    print("1. 实际工作空间可能因机器人型号和安装方式而异")
    print("2. 建议先在小范围内测试")
    print("3. 考虑工具长度和安全距离")

def main():
    """主函数"""
    print("🚀 开始G-code位置诊断\n")
    
    # 诊断G-code位置
    diagnose_gcode_positions()
    
    # 建议工作空间设置
    suggest_workspace_settings()
    
    print("\n" + "=" * 60)
    print("🎉 诊断完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
