#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 路径执行程序 (修复版)

修复内容:
1. 添加坐标系转换和缩放
2. 添加工作台原点偏移
3. 添加角度单位转换
4. 添加安全检查和错误处理
5. 添加调试输出
"""

import sys
import os
import time
import re
import math

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

# --- 全局参数配置 ---

# 1. 要执行的G-code文件
GCODE_FILE = "jiyi.Gcode"

# 2. 机械臂上对应的用户坐标系编号
USER_COORD_NUMBER = 1

# 3. 运动参数
VELOCITY_PERCENT = 30  # 降低速度以提高安全性
ACCEL_PERCENT = 20     # 降低加速度
SMOOTHING_LEVEL = 0    # 精确定位
TIMEOUT_SECONDS = 60   

# 4. 坐标转换参数 (关键修复!)
GCODE_SCALE = 0.001  # G-code坐标缩放因子 (mm -> m)
# 工作台原点在机器人坐标系中的位置 (基于示教盒当前位置调整)
WORKBENCH_ORIGIN_ROBOT = [0.661651, -0.0042, 1.078137]  # (m)
USE_DEGREE_ANGLES = True   # G-code角度是否为度数
ROBOT_API_USES_RADIANS = True  # 机器人API使用弧度单位 (重要发现!)

# 5. 工具垂直向下的默认姿态 (根据API实际读取值 - 弧度单位!)
# 示教盒显示: A=179.994°, B=-0.546°, C=0.000°
# API读取值: RX=3.141503, RY=-0.009545, RZ=0.000000 (弧度)
DEFAULT_VERTICAL_POSE = {
    'rx': 3.141503,   # 对应A轴，绕X轴旋转 (≈180°，工具垂直向下)
    'ry': -0.009545,  # 对应B轴，绕Y轴旋转 (≈-0.547°，微小偏差)
    'rz': 0.000000    # 对应C轴，绕Z轴旋转 (0°，无旋转)
}

# 6. 工具垂直向下的关节角度参考 (用于验证)
VERTICAL_JOINT_ANGLES_REF = [-0.001, 26.756, -27.288, -0.003, 90.009, -0.003]

# 7. 安全参数
MAX_COORDINATE_VALUE = 2.0  # 最大坐标值 (m)
MIN_Z_VALUE = 0.1  # 最小Z值 (m)，防止撞击工作台

def gcode_to_robot_coordinates(gcode_point, actual_vertical_pose=None):
    """
    将G-code坐标转换为机器人坐标系
    这是修复的关键函数!

    Args:
        gcode_point: G-code路径点
        actual_vertical_pose: 实际测量的垂直向下姿态，如果为None则使用默认值
    """
    # 1. 缩放G-code坐标 (mm -> m)
    x_scaled = gcode_point['x'] * GCODE_SCALE
    y_scaled = gcode_point['y'] * GCODE_SCALE
    z_scaled = gcode_point['z'] * GCODE_SCALE

    # 2. 添加工作台原点偏移
    x_robot = WORKBENCH_ORIGIN_ROBOT[0] + x_scaled
    y_robot = WORKBENCH_ORIGIN_ROBOT[1] + y_scaled
    z_robot = WORKBENCH_ORIGIN_ROBOT[2] + z_scaled

    # 3. 姿态处理 - 关键修复!
    # 检查G-code是否包含ABC角度，如果没有则使用实际测量的垂直向下姿态
    has_orientation = (gcode_point['a'] != 0.0 or
                      gcode_point['b'] != 0.0 or
                      gcode_point['c'] != 0.0)

    if has_orientation:
        # G-code包含姿态信息，使用指定的ABC角度
        if USE_DEGREE_ANGLES and ROBOT_API_USES_RADIANS:
            # G-code是度数，但机器人API需要弧度
            rx_robot = math.radians(gcode_point['a'])  # A轴 -> RX
            ry_robot = math.radians(gcode_point['b'])  # B轴 -> RY
            rz_robot = math.radians(gcode_point['c'])  # C轴 -> RZ
        elif USE_DEGREE_ANGLES and not ROBOT_API_USES_RADIANS:
            # G-code是度数，机器人API也用度数
            rx_robot = gcode_point['a']
            ry_robot = gcode_point['b']
            rz_robot = gcode_point['c']
        else:
            # G-code是弧度
            rx_robot = gcode_point['a']
            ry_robot = gcode_point['b']
            rz_robot = gcode_point['c']
    else:
        # G-code没有姿态信息，使用实际测量的垂直向下姿态 (已经是弧度)
        vertical_pose = actual_vertical_pose if actual_vertical_pose else DEFAULT_VERTICAL_POSE
        rx_robot = vertical_pose['rx']
        ry_robot = vertical_pose['ry']
        rz_robot = vertical_pose['rz']

    return {
        'x': x_robot,
        'y': y_robot,
        'z': z_robot,
        'rx': rx_robot,
        'ry': ry_robot,
        'rz': rz_robot,
        'has_orientation': has_orientation
    }

def validate_robot_coordinates(robot_point):
    """
    验证机器人坐标是否安全
    """
    # 检查坐标范围
    for coord in ['x', 'y', 'z']:
        if abs(robot_point[coord]) > MAX_COORDINATE_VALUE:
            print(f"❌ 坐标超出安全范围: {coord}={robot_point[coord]:.3f}m")
            return False
    
    # 检查Z值不能太低
    if robot_point['z'] < MIN_Z_VALUE:
        print(f"❌ Z坐标过低，可能撞击工作台: Z={robot_point['z']:.3f}m")
        return False
        
    return True

def get_current_cartesian_pose(socket_fd):
    """获取当前笛卡尔位姿"""
    try:
        current_pose = [0.0] * 6  # [X, Y, Z, RX, RY, RZ]
        result = nrc.get_current_position(socket_fd, current_pose)
        if isinstance(result, list) and len(result) > 6:
            return result[1:7]  # 返回 [X, Y, Z, RX, RY, RZ]
        return None
    except Exception as e:
        print(f"❌ 获取当前位姿失败: {e}")
        return None

def test_vertical_pose_with_robot(socket_fd):
    """
    测试工具垂直向下姿态是否正确
    1. 移动到参考的垂直向下关节角度位置
    2. 读取此时的笛卡尔姿态角度
    3. 更新默认垂直姿态参数
    """
    print("\n🔧 测试工具垂直向下姿态...")

    try:
        # 使用关节空间运动到垂直向下位置
        joint_cmd = nrc.MoveCmd()
        joint_cmd.targetPosType = 0  # 关节坐标
        joint_cmd.targetPosValue = nrc.VectorDouble()

        for angle in VERTICAL_JOINT_ANGLES_REF:
            joint_cmd.targetPosValue.append(angle)

        joint_cmd.velocity = 20  # 较慢的速度
        joint_cmd.acc = 10
        joint_cmd.dec = 10
        joint_cmd.pl = 0

        print(f"  移动到垂直向下关节角度: {VERTICAL_JOINT_ANGLES_REF}")
        result = nrc.robot_movej(socket_fd, joint_cmd)

        if result != 0:
            print(f"❌ 关节运动失败，错误码: {result}")
            return False, None

        if not wait_for_motion_complete(socket_fd, 30):
            print("❌ 运动超时")
            return False, None

        print("✅ 已移动到垂直向下位置")

        # 读取当前笛卡尔姿态
        current_pose = get_current_cartesian_pose(socket_fd)
        if current_pose:
            x, y, z, rx, ry, rz = current_pose
            print(f"  当前笛卡尔位姿: X={x:.3f}, Y={y:.3f}, Z={z:.3f}")
            print(f"  当前笛卡尔姿态: RX={rx:.3f}, RY={ry:.3f}, RZ={rz:.3f}")

            print("请确认工具确实垂直向下，然后按Enter继续...")
            input()

            # 返回真正的垂直向下笛卡尔姿态
            vertical_pose = {'rx': rx, 'ry': ry, 'rz': rz}
            print(f"✅ 已获取真正的垂直向下笛卡尔姿态: {vertical_pose}")
            return True, vertical_pose
        else:
            print("❌ 无法读取当前笛卡尔姿态")
            return False, None

    except Exception as e:
        print(f"❌ 测试垂直姿态时发生错误: {e}")
        return False, None

def parse_gcode_file(filepath):
    """
    解析G-code文件，提取G1指令的路径点
    """
    print(f"📄 正在解析G-code文件: {filepath}")
    path = []
    
    coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip().upper()
                if not line or line.startswith(';'):
                    continue

                if line.startswith('G1') or line.startswith('G0'):
                    coords = dict(coord_regex.findall(line))
                    if 'X' in coords and 'Y' in coords and 'Z' in coords:
                        point = {
                            'x': float(coords.get('X', 0.0)),
                            'y': float(coords.get('Y', 0.0)),
                            'z': float(coords.get('Z', 0.0)),
                            'a': float(coords.get('A', 0.0)),
                            'b': float(coords.get('B', 0.0)),
                            'c': float(coords.get('C', 0.0)),
                            'line_num': line_num
                        }
                        path.append(point)
        
        print(f"✅ 解析完成，共找到 {len(path)} 个路径点")
        
        # 显示前几个点的转换结果用于调试
        print("\n🔍 前3个点的坐标转换预览:")
        for i, gcode_point in enumerate(path[:3]):
            robot_point = gcode_to_robot_coordinates(gcode_point)  # 使用默认姿态预览
            orientation_info = "有姿态" if robot_point['has_orientation'] else "默认垂直"
            print(f"  点{i+1}: G-code({gcode_point['x']:.1f},{gcode_point['y']:.1f},{gcode_point['z']:.1f}) -> "
                  f"Robot({robot_point['x']:.3f},{robot_point['y']:.3f},{robot_point['z']:.3f}) [{orientation_info}]")
        
        return path
    except FileNotFoundError:
        print(f"❌ 错误：G-code文件未找到: {filepath}")
        return None
    except Exception as e:
        print(f"❌ 解析G-code文件时发生错误: {e}")
        return None

def wait_for_motion_complete(socket_fd, timeout_seconds=30):
    """等待机器人运动完成"""
    print("  ⏳ 正在等待机器人运动完成...", end="", flush=True)
    start_time = time.time()
    last_print_time = 0

    while time.time() - start_time < timeout_seconds:
        try:
            running_status = 0
            result = nrc.get_robot_running_state(socket_fd, running_status)
            if isinstance(result, list) and len(result) > 1:
                running_status = result[1]

            if running_status == 0:
                print(" ✅")
                return True

            if time.time() - last_print_time > 2:
                 print(".", end="", flush=True)
                 last_print_time = time.time()

            time.sleep(0.1)
        except Exception as e:
            print(f"\n  (检查运动状态时发生错误: {e})")
            time.sleep(0.5)

    print(" ❌ 超时!")
    return False

def robot_power_on_if_needed(socket_fd):
    """如果需要则执行上电操作"""
    try:
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]

        if servo_status == 3:
            print("✅ 机器人伺服已上电。")
            return True

        print("ℹ️ 机器人需要上电，开始上电流程...")
        nrc.clear_error(socket_fd)
        time.sleep(0.2)
        if servo_status == 0:
            nrc.set_servo_state(socket_fd, 1)
            time.sleep(0.2)

        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 上电失败！返回码: {result}。请检查安全回路、急停按钮等。")
            return False

        time.sleep(1.5)

        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1 and result[1] == 3:
            print("✅ 机器人上电成功！")
            return True
        else:
            print(f"❌ 上电后状态异常: {result}")
            return False
    except Exception as e:
        print(f"❌ 上电过程失败: {e}")
        return False

def robot_power_off(socket_fd):
    """机器人下电操作"""
    print("\nℹ️ 正在安全下电...")
    try:
        nrc.set_servo_poweroff(socket_fd)
        time.sleep(1)
        print("✅ 机器人已下电。")
        return True
    except Exception as e:
        print(f"❌ 下电过程失败: {e}")
        return False

def execute_gcode_on_robot():
    """主执行函数 - 修复版"""
    print("=" * 60)
    print("INEXBOT机械臂 G-code 路径执行程序 (修复版)")
    print("=" * 60)

    # 1. 解析G-code文件
    gcode_path = parse_gcode_file(GCODE_FILE)
    if not gcode_path:
        return

    socket_fd = -1
    try:
        # 2. 连接机械臂
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd <= 0:
            print("❌ 连接失败！")
            return
        print(f"✅ 连接成功！Socket ID: {socket_fd}")

        # 3. 检查并上电机器人
        if not robot_power_on_if_needed(socket_fd):
            return

        # 4. 设置运动所需的用户坐标系
        print(f"ℹ️ 设置当前用户坐标系为: {USER_COORD_NUMBER}")
        result = nrc.set_user_coord_number(socket_fd, USER_COORD_NUMBER)
        if result != 0:
            print(f"❌ 设置用户坐标系失败，错误码: {result}")
            return
        time.sleep(0.2)

        # 4.5 重要：获取真正的垂直向下姿态
        print(f"\n❓ 是否要先测试工具垂直向下姿态并获取真实姿态角度？(y/n): ", end="")
        test_pose = input().strip().lower()
        actual_vertical_pose = None

        if test_pose == 'y':
            success, actual_vertical_pose = test_vertical_pose_with_robot(socket_fd)
            if not success:
                print("⚠️ 垂直姿态测试失败，使用默认姿态...")
                actual_vertical_pose = DEFAULT_VERTICAL_POSE
            else:
                print(f"✅ 将使用实际测量的垂直姿态: {actual_vertical_pose}")
        else:
            print("ℹ️ 跳过垂直姿态测试，使用默认姿态")
            actual_vertical_pose = DEFAULT_VERTICAL_POSE

        # 5. 循环执行路径点 (修复版)
        print("\n" + "=" * 40)
        print(f"🚀 即将开始执行 {len(gcode_path)} 个路径点的移动...")
        print("=" * 40)

        successful_moves = 0
        failed_moves = 0

        for i, gcode_point in enumerate(gcode_path):
            print(f"\n--- 移动到点 {i+1}/{len(gcode_path)} (G-code第{gcode_point['line_num']}行) ---")

            # 关键修复: 坐标转换，使用实际测量的垂直姿态
            robot_point = gcode_to_robot_coordinates(gcode_point, actual_vertical_pose)

            # 安全检查
            if not validate_robot_coordinates(robot_point):
                print(f"❌ 跳过不安全的点 {i+1}")
                failed_moves += 1
                continue

            target_pos = [robot_point['x'], robot_point['y'], robot_point['z'],
                         robot_point['rx'], robot_point['ry'], robot_point['rz']]

            orientation_type = "指定姿态" if robot_point['has_orientation'] else "默认垂直"
            print(f"  G-code原始: X={gcode_point['x']:.1f}, Y={gcode_point['y']:.1f}, Z={gcode_point['z']:.1f}")
            print(f"  机器人目标: X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")
            print(f"  姿态 ({orientation_type}): RX={target_pos[3]:.3f}, RY={target_pos[4]:.3f}, RZ={target_pos[5]:.3f}")

            move_cmd = nrc.MoveCmd()
            move_cmd.targetPosType = 1  # 笛卡尔坐标
            move_cmd.targetPosValue = nrc.VectorDouble()
            for val in target_pos:
                move_cmd.targetPosValue.append(val)

            move_cmd.coord = 3  # 用户坐标系类型
            move_cmd.userNum = USER_COORD_NUMBER
            move_cmd.velocity = VELOCITY_PERCENT
            move_cmd.acc = ACCEL_PERCENT
            move_cmd.dec = ACCEL_PERCENT
            move_cmd.pl = SMOOTHING_LEVEL

            # 发送直线运动指令
            result = nrc.robot_movel(socket_fd, move_cmd)
            if result != 0:
                print(f"❌ 移动命令发送失败，错误码: {result}")
                failed_moves += 1
                continue

            # 等待运动完成
            if not wait_for_motion_complete(socket_fd, timeout_seconds=TIMEOUT_SECONDS):
                print("❌ 运动超时，跳过此点。")
                failed_moves += 1
                continue

            successful_moves += 1

        print("\n" + "=" * 40)
        print(f"🎉 路径执行完毕！成功: {successful_moves}, 失败: {failed_moves}")
        print("=" * 40)

    except Exception as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
    finally:
        # 6. 确保安全下电并断开连接
        if socket_fd > 0:
            robot_power_off(socket_fd)
            print("🔌 正在断开连接...")
            nrc.disconnect_robot(socket_fd)
            print("✅ 连接已断开。")

if __name__ == "__main__":
    execute_gcode_on_robot()
