#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 路径执行程序 (支持ABC角度，智能运动策略)

功能:
1. 读取一个标准的G-code文件。
2. 解析其中的G1移动指令 (支持X, Y, Z位置和A, B, C角度)。
3. 连接到INEXBOT机械臂。
4. 采用"关节运动负责接近，直线运动负责工作"的策略：
   - 使用robot_movej接近G-code第一个点和跳转不连续段落
   - 使用robot_movel进行精确的直线打印工作
5. 在指定的用户坐标系下执行G-code路径。
6. 监控每一步移动，确保完成后再执行下一步。
7. 任务结束后安全下电并断开连接。

增强功能:
- 支持ABC角度解析和转换为RX/RY/RZ
- 智能检测不连续段落
- 混合使用movej和movel运动策略
"""

import sys
import os
import time
import re
import math

# 添加lib目录到系统路径 (请根据您的项目结构调整)
# 假设此脚本与您之前的测试脚本在同一目录下
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

# --- 全局参数配置 ---

# 1. 要执行的G-code文件
#    请将您想要执行的G-code文件放在此脚本的同级目录下
GCODE_FILE = "jiyi.Gcode"

# 2. 机械臂上对应的用户坐标系编号
#    这个编号必须与G-code路径所基于的坐标系严格对应
USER_COORD_NUMBER = 1

# 3. 运动参数
VELOCITY_PERCENT = 50  # 速度百分比 (0-100)
ACCEL_PERCENT = 40     # 加速度百分比 (0-100)
SMOOTHING_LEVEL = 0    # 平滑度 (0-8, 0表示精确定位)
TIMEOUT_SECONDS = 60   # 单步运动的超时时间 (秒)

# 4. ABC角度处理参数
USE_ABC_ANGLES = True          # 是否使用G-code中的ABC角度
GCODE_ANGLES_IN_DEGREES = True # G-code中的角度是否为度数 (True=度数, False=弧度)
ROBOT_API_USES_RADIANS = True  # 机器人API是否使用弧度 (True=弧度, False=度数)

# 5. 运动策略参数
USE_SMART_MOVEMENT = True      # 是否使用智能运动策略 (movej接近 + movel工作)
DISCONTINUITY_THRESHOLD = 50.0 # 不连续段落检测阈值 (mm)
APPROACH_VELOCITY = 50         # 接近运动速度百分比 (movej)
WORK_VELOCITY = 50            # 工作运动速度百分比 (movel)

# 6. 默认姿态参数 (工具垂直向下的典型姿态)
# 根据关节角度 J1≈0°, J2≈27°, J3≈-27°, J4≈0°, J5≈90°, J6≈0° 对应的笛卡尔姿态
DEFAULT_RX = 0.0       # 绕X轴旋转角度 (度)
DEFAULT_RY = 0.0       # 绕Y轴旋转角度 (度)
DEFAULT_RZ = 0.0       # 绕Z轴旋转角度 (度)

# 7. 调试和日志选项
VERBOSE_OUTPUT = True          # 是否显示详细输出
SHOW_GCODE_DETAILS = True      # 是否显示G-code详细信息
PAUSE_BETWEEN_SEGMENTS = False # 是否在段落间暂停等待用户确认

# 8. 安全参数
ENABLE_SAFETY_CHECKS = False   # 是否启用安全检查 (暂时禁用来测试)
MAX_SINGLE_MOVE_DISTANCE = 200.0  # 单次移动最大距离 (mm)
MIN_Z_COORDINATE = -50.0       # 最小Z坐标 (mm)，防止撞击工作台

# 9. 工作空间限制 (根据INEXBOT机器人实际工作空间调整)
# 基于用户坐标系1原点: X=664.153, Y=-0.004, Z=1198.230
WORKSPACE_LIMITS = {
    'x_min': -1000.0, 'x_max': 1000.0,   # X轴范围 (mm) - 扩大范围
    'y_min': -1000.0, 'y_max': 1000.0,   # Y轴范围 (mm) - 扩大范围
    'z_min': -200.0,  'z_max': 1500.0,   # Z轴范围 (mm) - 考虑用户坐标系Z=1198
    'rx_min': -360.0, 'rx_max': 360.0,   # RX角度范围 (度) - 扩大角度范围
    'ry_min': -360.0, 'ry_max': 360.0,   # RY角度范围 (度) - 扩大角度范围
    'rz_min': -360.0, 'rz_max': 360.0,   # RZ角度范围 (度) - 扩大角度范围
}

# 10. 调试选项
CHECK_REACHABILITY = False     # 是否检查位置可达性 (暂时禁用避免问题)
SHOW_CURRENT_POSITION = True   # 是否显示当前位置
DRY_RUN_MODE = True           # 干运行模式（只检查不执行，建议先启用）
SKIP_FIRST_N_POINTS = 0       # 跳过前N个点（用于调试）


def parse_gcode_file(filepath):
    """
    解析G-code文件，提取G1指令的路径点。
    返回一个包含XYZ位置和ABC角度的字典列表。
    """
    print(f"📄 正在解析G-code文件: {filepath}")
    path = []

    # 正则表达式用于匹配G0/G1指令中的坐标轴和值（支持XYZABC）
    coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip().upper()
                # 忽略注释和空行
                if not line or line.startswith(';'):
                    continue

                if line.startswith('G1') or line.startswith('G0'):
                    coords = dict(coord_regex.findall(line))

                    # 检查是否包含必需的XYZ坐标
                    if 'X' in coords and 'Y' in coords and 'Z' in coords:
                        point = {
                            'x': float(coords.get('X', 0.0)),
                            'y': float(coords.get('Y', 0.0)),
                            'z': float(coords.get('Z', 0.0)),
                            'a': float(coords.get('A', 0.0)),  # A轴角度 (绕X轴)
                            'b': float(coords.get('B', 0.0)),  # B轴角度 (绕Y轴)
                            'c': float(coords.get('C', 0.0)),  # C轴角度 (绕Z轴)
                            'has_abc': any(axis in coords for axis in ['A', 'B', 'C']),
                            'line_num': line_num,
                            'original_line': line.strip()
                        }
                        path.append(point)

        # 统计信息
        points_with_abc = sum(1 for p in path if p['has_abc'])
        print(f"✅ 解析完成，共找到 {len(path)} 个路径点。")
        print(f"   其中 {points_with_abc} 个点包含ABC角度信息。")

        if USE_ABC_ANGLES and points_with_abc > 0:
            print(f"   将使用G-code中的ABC角度信息。")
        elif points_with_abc > 0:
            print(f"   检测到ABC角度但配置为忽略，将使用默认姿态。")

        return path
    except FileNotFoundError:
        print(f"❌ 错误：G-code文件未找到: {filepath}")
        return None
    except Exception as e:
        print(f"❌ 解析G-code文件时发生错误: {e}")
        return None


def convert_abc_to_rxryrz(point, default_orientation):
    """
    将G-code中的ABC角度转换为机器人的RX/RY/RZ角度。

    Args:
        point: G-code路径点字典，包含a, b, c角度
        default_orientation: 默认姿态 [rx, ry, rz]

    Returns:
        [rx, ry, rz] 机器人姿态角度
    """
    if not USE_ABC_ANGLES or not point['has_abc']:
        # 不使用ABC角度或点不包含ABC信息，返回默认姿态
        return default_orientation

    # 获取ABC角度
    a_angle = point['a']  # A轴 -> RX (绕X轴旋转)
    b_angle = point['b']  # B轴 -> RY (绕Y轴旋转)
    c_angle = point['c']  # C轴 -> RZ (绕Z轴旋转)

    # 角度单位转换
    if GCODE_ANGLES_IN_DEGREES and ROBOT_API_USES_RADIANS:
        # G-code是度数，机器人API需要弧度
        rx = math.radians(a_angle)
        ry = math.radians(b_angle)
        rz = math.radians(c_angle)
    elif GCODE_ANGLES_IN_DEGREES and not ROBOT_API_USES_RADIANS:
        # G-code是度数，机器人API也用度数
        rx = a_angle
        ry = b_angle
        rz = c_angle
    else:
        # G-code是弧度，直接使用
        rx = a_angle
        ry = b_angle
        rz = c_angle

    return [rx, ry, rz]


def calculate_distance_3d(point1, point2):
    """计算两个3D点之间的距离"""
    dx = point2['x'] - point1['x']
    dy = point2['y'] - point1['y']
    dz = point2['z'] - point1['z']
    return math.sqrt(dx*dx + dy*dy + dz*dz)


def detect_discontinuous_segments(path):
    """
    检测G-code路径中的不连续段落。

    Args:
        path: G-code路径点列表

    Returns:
        segments: 段落列表，每个段落是连续的路径点索引列表
    """
    if not path:
        return []

    segments = []
    current_segment = [0]  # 从第一个点开始

    for i in range(1, len(path)):
        distance = calculate_distance_3d(path[i-1], path[i])

        if distance > DISCONTINUITY_THRESHOLD:
            # 发现不连续，结束当前段落，开始新段落
            segments.append(current_segment)
            current_segment = [i]
            print(f"  检测到不连续段落：点{i-1}到点{i}距离{distance:.2f}mm > 阈值{DISCONTINUITY_THRESHOLD}mm")
        else:
            # 连续点，添加到当前段落
            current_segment.append(i)

    # 添加最后一个段落
    if current_segment:
        segments.append(current_segment)

    print(f"✅ 检测到 {len(segments)} 个连续段落")
    for i, segment in enumerate(segments):
        print(f"   段落{i+1}: 点{segment[0]+1}-{segment[-1]+1} ({len(segment)}个点)")

    return segments


def validate_target_position(target_pos, point_idx):
    """
    验证目标位置的安全性和可达性。

    Args:
        target_pos: 目标位置 [x, y, z, rx, ry, rz]
        point_idx: 点索引

    Returns:
        bool: 位置是否安全且可达
    """
    if not ENABLE_SAFETY_CHECKS:
        return True

    x, y, z = target_pos[0], target_pos[1], target_pos[2]
    rx, ry, rz = target_pos[3], target_pos[4], target_pos[5]

    # 检查坐标是否为有效数值
    for i, coord in enumerate(['X', 'Y', 'Z', 'RX', 'RY', 'RZ']):
        if not math.isfinite(target_pos[i]):
            print(f"    ❌ 错误：点{point_idx+1}的{coord}坐标不是有效数值: {target_pos[i]}")
            return False

    # 检查工作空间限制
    if x < WORKSPACE_LIMITS['x_min'] or x > WORKSPACE_LIMITS['x_max']:
        print(f"    ❌ 位置超出工作空间：点{point_idx+1}的X坐标({x:.3f}mm)超出范围[{WORKSPACE_LIMITS['x_min']}, {WORKSPACE_LIMITS['x_max']}]")
        return False

    if y < WORKSPACE_LIMITS['y_min'] or y > WORKSPACE_LIMITS['y_max']:
        print(f"    ❌ 位置超出工作空间：点{point_idx+1}的Y坐标({y:.3f}mm)超出范围[{WORKSPACE_LIMITS['y_min']}, {WORKSPACE_LIMITS['y_max']}]")
        return False

    if z < WORKSPACE_LIMITS['z_min'] or z > WORKSPACE_LIMITS['z_max']:
        print(f"    ❌ 位置超出工作空间：点{point_idx+1}的Z坐标({z:.3f}mm)超出范围[{WORKSPACE_LIMITS['z_min']}, {WORKSPACE_LIMITS['z_max']}]")
        return False

    # 检查姿态角度限制（转换为度数检查）
    rx_deg = math.degrees(rx) if ROBOT_API_USES_RADIANS else rx
    ry_deg = math.degrees(ry) if ROBOT_API_USES_RADIANS else ry
    rz_deg = math.degrees(rz) if ROBOT_API_USES_RADIANS else rz

    if rx_deg < WORKSPACE_LIMITS['rx_min'] or rx_deg > WORKSPACE_LIMITS['rx_max']:
        print(f"    ❌ 姿态超出范围：点{point_idx+1}的RX角度({rx_deg:.3f}°)超出范围[{WORKSPACE_LIMITS['rx_min']}, {WORKSPACE_LIMITS['rx_max']}]")
        return False

    if ry_deg < WORKSPACE_LIMITS['ry_min'] or ry_deg > WORKSPACE_LIMITS['ry_max']:
        print(f"    ❌ 姿态超出范围：点{point_idx+1}的RY角度({ry_deg:.3f}°)超出范围[{WORKSPACE_LIMITS['ry_min']}, {WORKSPACE_LIMITS['ry_max']}]")
        return False

    if rz_deg < WORKSPACE_LIMITS['rz_min'] or rz_deg > WORKSPACE_LIMITS['rz_max']:
        print(f"    ❌ 姿态超出范围：点{point_idx+1}的RZ角度({rz_deg:.3f}°)超出范围[{WORKSPACE_LIMITS['rz_min']}, {WORKSPACE_LIMITS['rz_max']}]")
        return False

    return True


def check_position_reachability(socket_fd, target_pos, point_idx):
    """
    检查位置是否可达（通过尝试计算逆运动学）

    Args:
        socket_fd: 机器人连接句柄
        target_pos: 目标位置 [x, y, z, rx, ry, rz]
        point_idx: 点索引

    Returns:
        bool: 位置是否可达
    """
    if not CHECK_REACHABILITY:
        return True

    try:
        # 创建临时MoveCmd来测试可达性
        test_cmd = nrc.MoveCmd()
        test_cmd.targetPosType = 1  # 笛卡尔坐标
        test_cmd.targetPosValue = nrc.VectorDouble()
        for val in target_pos:
            test_cmd.targetPosValue.append(val)

        test_cmd.coord = 3  # 用户坐标系类型
        test_cmd.userNum = USER_COORD_NUMBER

        # 尝试计算逆运动学（不实际移动）
        # 注意：这里需要根据实际的NRC接口API调整
        # 如果没有专门的逆运动学检查函数，可以先跳过这个检查
        print(f"    ℹ️ 位置可达性检查：点{point_idx+1} - 假设可达")
        return True

    except Exception as e:
        print(f"    ❌ 位置可达性检查失败：点{point_idx+1} - {e}")
        return False


def show_current_robot_position(socket_fd):
    """显示当前机器人位置"""
    if not SHOW_CURRENT_POSITION:
        return

    try:
        current_pose = get_current_pose(socket_fd)
        if current_pose:
            print(f"📍 当前机器人位置:")
            print(f"   位置: X={current_pose[0]:.3f}, Y={current_pose[1]:.3f}, Z={current_pose[2]:.3f}")
            print(f"   姿态: RX={current_pose[3]:.3f}, RY={current_pose[4]:.3f}, RZ={current_pose[5]:.3f}")
        else:
            print("⚠️ 无法获取当前机器人位置")
    except Exception as e:
        print(f"⚠️ 获取当前位置时出错: {e}")


def analyze_gcode_workspace(gcode_path):
    """分析G-code文件的工作空间需求"""
    if not gcode_path:
        return

    print("\n" + "=" * 50)
    print("📐 G-code工作空间分析")
    print("=" * 50)

    # 计算坐标范围
    x_coords = [p['x'] for p in gcode_path]
    y_coords = [p['y'] for p in gcode_path]
    z_coords = [p['z'] for p in gcode_path]

    x_min, x_max = min(x_coords), max(x_coords)
    y_min, y_max = min(y_coords), max(y_coords)
    z_min, z_max = min(z_coords), max(z_coords)

    print(f"📊 坐标范围:")
    print(f"   X: {x_min:.3f} ~ {x_max:.3f} mm (跨度: {x_max-x_min:.3f} mm)")
    print(f"   Y: {y_min:.3f} ~ {y_max:.3f} mm (跨度: {y_max-y_min:.3f} mm)")
    print(f"   Z: {z_min:.3f} ~ {z_max:.3f} mm (跨度: {z_max-z_min:.3f} mm)")

    # 检查是否超出当前工作空间设置
    workspace_ok = True
    if x_min < WORKSPACE_LIMITS['x_min'] or x_max > WORKSPACE_LIMITS['x_max']:
        print(f"⚠️ X坐标超出工作空间限制 [{WORKSPACE_LIMITS['x_min']}, {WORKSPACE_LIMITS['x_max']}]")
        workspace_ok = False

    if y_min < WORKSPACE_LIMITS['y_min'] or y_max > WORKSPACE_LIMITS['y_max']:
        print(f"⚠️ Y坐标超出工作空间限制 [{WORKSPACE_LIMITS['y_min']}, {WORKSPACE_LIMITS['y_max']}]")
        workspace_ok = False

    if z_min < WORKSPACE_LIMITS['z_min'] or z_max > WORKSPACE_LIMITS['z_max']:
        print(f"⚠️ Z坐标超出工作空间限制 [{WORKSPACE_LIMITS['z_min']}, {WORKSPACE_LIMITS['z_max']}]")
        workspace_ok = False

    if workspace_ok:
        print("✅ 所有坐标都在当前工作空间限制内")
    else:
        print("\n💡 建议的工作空间设置:")
        margin = 50  # 50mm安全边距
        print(f"   X: {x_min-margin:.0f} ~ {x_max+margin:.0f}")
        print(f"   Y: {y_min-margin:.0f} ~ {y_max+margin:.0f}")
        print(f"   Z: {max(z_min-margin, -100):.0f} ~ {z_max+margin:.0f}")

    # 分析ABC角度范围
    if USE_ABC_ANGLES:
        abc_points = [p for p in gcode_path if p['has_abc']]
        if abc_points:
            a_coords = [p['a'] for p in abc_points]
            b_coords = [p['b'] for p in abc_points]
            c_coords = [p['c'] for p in abc_points]

            print(f"\n🔄 ABC角度范围:")
            print(f"   A: {min(a_coords):.3f}° ~ {max(a_coords):.3f}°")
            print(f"   B: {min(b_coords):.3f}° ~ {max(b_coords):.3f}°")
            print(f"   C: {min(c_coords):.3f}° ~ {max(c_coords):.3f}°")

    print("=" * 50)


def print_execution_summary(gcode_path, segments):
    """打印执行摘要信息"""
    print("\n" + "=" * 50)
    print("📋 G-code执行计划摘要")
    print("=" * 50)
    print(f"📁 文件: {GCODE_FILE}")
    print(f"📊 总点数: {len(gcode_path)}")
    print(f"🔧 用户坐标系: {USER_COORD_NUMBER}")
    print(f"⚙️ ABC角度支持: {'启用' if USE_ABC_ANGLES else '禁用'}")
    print(f"🎯 智能运动策略: {'启用' if USE_SMART_MOVEMENT else '禁用'}")
    print(f"🛡️ 安全检查: {'启用' if ENABLE_SAFETY_CHECKS else '禁用'}")
    print(f"🔍 可达性检查: {'启用' if CHECK_REACHABILITY else '禁用'}")
    print(f"🧪 干运行模式: {'启用' if DRY_RUN_MODE else '禁用'}")

    if USE_SMART_MOVEMENT:
        print(f"📈 段落数: {len(segments)}")
        print(f"🚀 接近速度: {APPROACH_VELOCITY}%")
        print(f"🔨 工作速度: {WORK_VELOCITY}%")
        print(f"📏 不连续阈值: {DISCONTINUITY_THRESHOLD}mm")
    else:
        print(f"🔨 运动速度: {VELOCITY_PERCENT}%")

    # 统计ABC角度使用情况
    points_with_abc = sum(1 for p in gcode_path if p['has_abc'])
    if points_with_abc > 0:
        print(f"🔄 包含ABC角度的点: {points_with_abc}/{len(gcode_path)}")

    print("=" * 50)


# --- 从您提供的测试脚本中复用的核心函数 ---
# (为了代码完整性，这里复制了这些函数。您可以保持它们在外部lib中)

def wait_for_motion_complete(socket_fd, timeout_seconds=30):
    """等待机器人运动完成"""
    print("  ⏳ 正在等待机器人运动完成...", end="", flush=True)
    start_time = time.time()
    last_print_time = 0

    while time.time() - start_time < timeout_seconds:
        try:
            running_status = 0
            result = nrc.get_robot_running_state(socket_fd, running_status)
            if isinstance(result, list) and len(result) > 1:
                running_status = result[1]
            
            if running_status == 0:
                print(" ✅")
                return True
            
            if time.time() - last_print_time > 2: # 每2秒打印一次状态
                 print(".", end="", flush=True)
                 last_print_time = time.time()

            time.sleep(0.1)
        except Exception as e:
            print(f"\n  (检查运动状态时发生错误: {e})")
            time.sleep(0.5)

    print(" ❌ 超时!")
    return False

def robot_power_on_if_needed(socket_fd):
    """如果需要则执行上电操作"""
    try:
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
        
        if servo_status == 3:
            print("✅ 机器人伺服已上电。")
            return True
        
        print("ℹ️ 机器人需要上电，开始上电流程...")
        nrc.clear_error(socket_fd)
        time.sleep(0.2)
        if servo_status == 0:
            nrc.set_servo_state(socket_fd, 1)
            time.sleep(0.2)
        
        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 上电失败！返回码: {result}。请检查安全回路、急停按钮等。")
            return False
        
        time.sleep(1.5)
        
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1 and result[1] == 3:
            print("✅ 机器人上电成功！")
            return True
        else:
            print(f"❌ 上电后状态异常: {result}")
            return False
    except Exception as e:
        print(f"❌ 上电过程失败: {e}")
        return False

def robot_power_off(socket_fd):
    """机器人下电操作"""
    print("\nℹ️ 正在安全下电...")
    try:
        nrc.set_servo_poweroff(socket_fd)
        time.sleep(1)
        print("✅ 机器人已下电。")
        return True
    except Exception as e:
        print(f"❌ 下电过程失败: {e}")
        return False

def robot_movej_to_position(socket_fd, target_pos, description="关节运动"):
    """
    使用关节运动(movej)移动到目标位置。
    适用于接近运动和跳转不连续段落。

    Args:
        socket_fd: 机器人连接句柄
        target_pos: 目标位置 [x, y, z, rx, ry, rz]
        description: 运动描述

    Returns:
        bool: 运动是否成功
    """
    print(f"  🔄 {description}")
    print(f"    目标位置: X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")
    print(f"    目标姿态: RX={target_pos[3]:.3f}, RY={target_pos[4]:.3f}, RZ={target_pos[5]:.3f}")

    try:
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 1  # 笛卡尔坐标
        move_cmd.targetPosValue = nrc.VectorDouble()
        for val in target_pos:
            move_cmd.targetPosValue.append(val)

        move_cmd.coord = 3  # 用户坐标系类型
        move_cmd.userNum = USER_COORD_NUMBER
        move_cmd.velocity = APPROACH_VELOCITY  # 使用接近运动速度
        move_cmd.acc = ACCEL_PERCENT
        move_cmd.dec = ACCEL_PERCENT
        move_cmd.pl = SMOOTHING_LEVEL

        # 发送关节运动指令
        result = nrc.robot_movej(socket_fd, move_cmd)
        if result != 0:
            print(f"    ❌ 关节运动命令发送失败，错误码: {result}")
            return False

        # 等待运动完成
        if not wait_for_motion_complete(socket_fd, timeout_seconds=TIMEOUT_SECONDS):
            print("    ❌ 关节运动超时")
            return False

        print(f"    ✅ {description}完成")
        return True

    except Exception as e:
        print(f"    ❌ 关节运动过程中发生错误: {e}")
        return False

def robot_movel_to_position(socket_fd, target_pos, description="直线运动"):
    """
    使用直线运动(movel)移动到目标位置。
    适用于精确的工作运动。

    Args:
        socket_fd: 机器人连接句柄
        target_pos: 目标位置 [x, y, z, rx, ry, rz]
        description: 运动描述

    Returns:
        bool: 运动是否成功
    """
    try:
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 1  # 笛卡尔坐标
        move_cmd.targetPosValue = nrc.VectorDouble()
        for val in target_pos:
            move_cmd.targetPosValue.append(val)

        move_cmd.coord = 3  # 用户坐标系类型
        move_cmd.userNum = USER_COORD_NUMBER
        move_cmd.velocity = WORK_VELOCITY  # 使用工作运动速度
        move_cmd.acc = ACCEL_PERCENT
        move_cmd.dec = ACCEL_PERCENT
        move_cmd.pl = SMOOTHING_LEVEL

        # 发送直线运动指令
        result = nrc.robot_movel(socket_fd, move_cmd)
        if result != 0:
            print(f"    ❌ 直线运动命令发送失败，错误码: {result}")
            return False

        # 等待运动完成
        if not wait_for_motion_complete(socket_fd, timeout_seconds=TIMEOUT_SECONDS):
            print("    ❌ 直线运动超时")
            return False

        return True

    except Exception as e:
        print(f"    ❌ 直线运动过程中发生错误: {e}")
        return False

def get_current_pose(socket_fd):
    """获取机械臂当前位姿"""
    try:
        pos = nrc.VectorDouble()
        # 参数：socketFd, coord(坐标系类型), pos
        # coord: 0=关节，1=直角，2=工具，3=用户
        # 这里使用1=直角坐标系获取笛卡尔位置
        result = nrc.get_current_position(socket_fd, 1, pos)
        if result == 0 and len(pos) >= 6:
            return [pos[i] for i in range(6)]
        else:
            print(f"❌ 获取当前位姿失败，错误码: {result}")
            return None
    except Exception as e:
        print(f"❌ 获取当前位姿时发生错误: {e}")
        return None


def execute_gcode_on_robot():
    """主执行函数"""
    print("=" * 60)
    print("INEXBOT机械臂 G-code 路径执行程序")
    print("=" * 60)

    # 1. 解析G-code文件
    gcode_path = parse_gcode_file(GCODE_FILE)
    if not gcode_path:
        return

    socket_fd = -1
    try:
        # 2. 连接机械臂
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd <= 0:
            print("❌ 连接失败！")
            return
        print(f"✅ 连接成功！Socket ID: {socket_fd}")

        # 3. 检查并上电机器人
        if not robot_power_on_if_needed(socket_fd):
            return

        # 4. 设置运动所需的用户坐标系
        print(f"ℹ️ 设置当前用户坐标系为: {USER_COORD_NUMBER}")
        result = nrc.set_user_coord_number(socket_fd, USER_COORD_NUMBER)
        if result != 0:
            print(f"❌ 设置用户坐标系失败，错误码: {result}")
            return
        time.sleep(0.2)

        # 4.1 显示当前机器人位置
        show_current_robot_position(socket_fd)

        # 5. 获取当前姿态作为默认姿态（工具垂直向下）
        print("ℹ️ 获取当前机械臂姿态作为默认姿态...")
        current_pose = get_current_pose(socket_fd)
        if current_pose is None:
            print("❌ 无法获取当前姿态，使用预设默认值")
            default_orientation = [DEFAULT_RX, DEFAULT_RY, DEFAULT_RZ]
        else:
            default_orientation = [current_pose[3], current_pose[4], current_pose[5]]
            print(f"✅ 当前姿态: RX={default_orientation[0]:.3f}, RY={default_orientation[1]:.3f}, RZ={default_orientation[2]:.3f}")
            print("   将使用此姿态作为所有路径点的默认姿态")

        # 6. 检测不连续段落（如果启用智能运动策略）
        segments = []
        if USE_SMART_MOVEMENT:
            print("\n" + "=" * 40)
            print("🔍 检测G-code路径段落...")
            print("=" * 40)
            segments = detect_discontinuous_segments(gcode_path)
        else:
            # 不使用智能策略，将所有点作为一个段落
            segments = [list(range(len(gcode_path)))]
            print(f"\n⚠️ 智能运动策略已禁用，将使用传统的逐点直线运动")

        # 7. 分析G-code工作空间
        if VERBOSE_OUTPUT:
            analyze_gcode_workspace(gcode_path)

        # 8. 显示执行计划摘要
        if VERBOSE_OUTPUT:
            print_execution_summary(gcode_path, segments)

        # 8. 用户确认开始执行
        if PAUSE_BETWEEN_SEGMENTS:
            input("\n按Enter键开始执行G-code路径...")

        # 9. 执行路径点
        print("\n" + "=" * 40)
        print(f"🚀 开始执行 {len(gcode_path)} 个路径点的移动...")
        if USE_SMART_MOVEMENT:
            print(f"   策略: 关节运动接近 + 直线运动工作")
            print(f"   共 {len(segments)} 个段落")
        else:
            print(f"   策略: 传统直线运动")
        print("=" * 40)

        total_success = 0
        total_points = len(gcode_path)

        for segment_idx, segment in enumerate(segments):
            print(f"\n{'='*20} 段落 {segment_idx+1}/{len(segments)} {'='*20}")
            print(f"执行点 {segment[0]+1}-{segment[-1]+1} ({len(segment)}个点)")

            # 段落间暂停
            if PAUSE_BETWEEN_SEGMENTS and segment_idx > 0:
                input(f"按Enter键继续执行段落{segment_idx+1}...")

            for point_idx_in_segment, point_idx in enumerate(segment):
                # 跳过前N个点（用于调试）
                if point_idx < SKIP_FIRST_N_POINTS:
                    print(f"\n--- 跳过点 {point_idx+1}/{total_points} (调试设置) ---")
                    total_success += 1
                    continue

                point = gcode_path[point_idx]
                print(f"\n--- 点 {point_idx+1}/{total_points} (段落{segment_idx+1}中第{point_idx_in_segment+1}个) ---")

                # 计算目标位置和姿态
                target_orientation = convert_abc_to_rxryrz(point, default_orientation)
                target_pos = [point['x'], point['y'], point['z'],
                             target_orientation[0], target_orientation[1], target_orientation[2]]

                print(f"  目标位置: X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")
                print(f"  目标姿态: RX={target_pos[3]:.3f}, RY={target_pos[4]:.3f}, RZ={target_pos[5]:.3f}")

                if point['has_abc'] and SHOW_GCODE_DETAILS:
                    print(f"  G-code角度: A={point['a']:.3f}, B={point['b']:.3f}, C={point['c']:.3f}")

                # 安全检查
                if not validate_target_position(target_pos, point_idx):
                    print(f"  ❌ 点{point_idx+1}未通过安全检查，跳过")
                    continue

                # 可达性检查
                if not check_position_reachability(socket_fd, target_pos, point_idx):
                    print(f"  ❌ 点{point_idx+1}位置不可达，跳过")
                    continue

                # 干运行模式检查
                if DRY_RUN_MODE:
                    print(f"  🔍 干运行模式：点{point_idx+1}检查通过，跳过实际执行")
                    total_success += 1
                    continue

                # 选择运动策略
                success = False
                if USE_SMART_MOVEMENT and point_idx_in_segment == 0:
                    # 段落的第一个点：使用关节运动接近
                    if segment_idx == 0:
                        description = f"关节运动接近第一个G-code点"
                    else:
                        description = f"关节运动跳转到段落{segment_idx+1}起始点"
                    success = robot_movej_to_position(socket_fd, target_pos, description)
                else:
                    # 段落内的其他点：使用直线运动
                    success = robot_movel_to_position(socket_fd, target_pos, f"直线运动到点{point_idx+1}")

                if success:
                    total_success += 1
                    print(f"  ✅ 点{point_idx+1}移动成功")
                else:
                    print(f"  ❌ 点{point_idx+1}移动失败")
                    # 询问是否继续
                    continue_choice = input("  是否继续执行下一个点？(y/n): ").lower().strip()
                    if continue_choice not in ['y', 'yes', '是']:
                        print("🛑 用户选择停止执行")
                        break

            # 检查是否需要停止
            if total_success < point_idx + 1:
                break

        print("\n" + "=" * 40)
        print("🎉 G-code路径执行完毕！")
        print(f"✅ 成功执行: {total_success}/{total_points} 个点")
        if total_success == total_points:
            print("🎯 所有路径点都已成功执行！")
        else:
            print(f"⚠️ 有 {total_points - total_success} 个点未成功执行")
        print("=" * 40)

    except Exception as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
    finally:
        # 7. 确保安全下电并断开连接
        if socket_fd > 0:
            robot_power_off(socket_fd)
            print("🔌 正在断开连接...")
            nrc.disconnect_robot(socket_fd)
            print("✅ 连接已断开。")

if __name__ == "__main__":
    execute_gcode_on_robot()