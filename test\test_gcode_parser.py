#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试G-code解析器功能
验证ABC角度解析和段落检测功能
"""

import sys
import os
import math

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

# 导入run_gcode模块的函数
sys.path.append(os.path.dirname(__file__))
from run_gcode import (
    parse_gcode_file, 
    convert_abc_to_rxryrz, 
    detect_discontinuous_segments,
    calculate_distance_3d,
    validate_target_position,
    print_execution_summary
)

def test_gcode_parser():
    """测试G-code解析功能"""
    print("=" * 60)
    print("🧪 G-code解析器功能测试")
    print("=" * 60)
    
    # 测试解析test_abc_gcode.gcode文件
    test_file = "test_abc_gcode.gcode"
    print(f"\n📄 测试文件: {test_file}")
    
    # 解析G-code文件
    gcode_path = parse_gcode_file(test_file)
    
    if not gcode_path:
        print("❌ 解析失败")
        return False
    
    print(f"\n✅ 解析成功，共 {len(gcode_path)} 个点")
    
    # 显示解析结果
    print("\n📊 解析结果详情:")
    for i, point in enumerate(gcode_path):
        print(f"  点{i+1}: X={point['x']:6.1f} Y={point['y']:6.1f} Z={point['z']:6.1f}", end="")
        if point['has_abc']:
            print(f" A={point['a']:6.1f} B={point['b']:6.1f} C={point['c']:6.1f}", end="")
        print(f" (行{point['line_num']})")
    
    return True, gcode_path

def test_abc_conversion():
    """测试ABC角度转换功能"""
    print("\n" + "=" * 60)
    print("🔄 ABC角度转换测试")
    print("=" * 60)
    
    # 测试点
    test_points = [
        {'x': 10, 'y': 10, 'z': 5, 'a': 0, 'b': 0, 'c': 0, 'has_abc': True},
        {'x': 20, 'y': 20, 'z': 5, 'a': 45, 'b': -30, 'c': 90, 'has_abc': True},
        {'x': 30, 'y': 30, 'z': 5, 'a': 0, 'b': 0, 'c': 0, 'has_abc': False},
    ]
    
    default_orientation = [0.0, 0.0, 0.0]
    
    for i, point in enumerate(test_points):
        print(f"\n测试点{i+1}:")
        print(f"  输入: A={point['a']}, B={point['b']}, C={point['c']}, has_abc={point['has_abc']}")
        
        result = convert_abc_to_rxryrz(point, default_orientation)
        print(f"  输出: RX={result[0]:.6f}, RY={result[1]:.6f}, RZ={result[2]:.6f}")
        
        if point['has_abc']:
            # 验证角度转换（假设使用弧度）
            expected_rx = math.radians(point['a'])
            expected_ry = math.radians(point['b'])
            expected_rz = math.radians(point['c'])
            print(f"  期望: RX={expected_rx:.6f}, RY={expected_ry:.6f}, RZ={expected_rz:.6f}")

def test_segment_detection(gcode_path):
    """测试段落检测功能"""
    print("\n" + "=" * 60)
    print("📈 段落检测测试")
    print("=" * 60)
    
    # 测试不同的阈值
    thresholds = [20.0, 50.0, 100.0]
    
    for threshold in thresholds:
        print(f"\n🔍 阈值: {threshold}mm")
        
        # 临时修改全局阈值
        import run_gcode
        original_threshold = run_gcode.DISCONTINUITY_THRESHOLD
        run_gcode.DISCONTINUITY_THRESHOLD = threshold
        
        segments = detect_discontinuous_segments(gcode_path)
        
        print(f"  检测到 {len(segments)} 个段落:")
        for i, segment in enumerate(segments):
            print(f"    段落{i+1}: 点{segment[0]+1}-{segment[-1]+1} ({len(segment)}个点)")
        
        # 恢复原始阈值
        run_gcode.DISCONTINUITY_THRESHOLD = original_threshold

def test_safety_validation():
    """测试安全验证功能"""
    print("\n" + "=" * 60)
    print("🛡️ 安全验证测试")
    print("=" * 60)
    
    # 测试位置
    test_positions = [
        ([10, 10, 5, 0, 0, 0], "正常位置"),
        ([10, 10, -100, 0, 0, 0], "Z坐标过低"),
        ([10, 10, float('inf'), 0, 0, 0], "无效坐标"),
        ([10, 10, float('nan'), 0, 0, 0], "NaN坐标"),
    ]
    
    for i, (pos, desc) in enumerate(test_positions):
        print(f"\n测试{i+1}: {desc}")
        print(f"  位置: {pos}")
        result = validate_target_position(pos, i)
        print(f"  结果: {'✅ 通过' if result else '❌ 失败'}")

def main():
    """主测试函数"""
    print("🚀 开始G-code解析器功能测试\n")
    
    # 1. 测试G-code解析
    success, gcode_path = test_gcode_parser()
    if not success:
        return
    
    # 2. 测试ABC角度转换
    test_abc_conversion()
    
    # 3. 测试段落检测
    test_segment_detection(gcode_path)
    
    # 4. 测试安全验证
    test_safety_validation()
    
    # 5. 测试执行摘要
    print("\n" + "=" * 60)
    print("📋 执行摘要测试")
    print("=" * 60)
    
    import run_gcode
    segments = detect_discontinuous_segments(gcode_path)
    print_execution_summary(gcode_path, segments)
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
