#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本：获取机械臂当前的笛卡尔姿态
用于验证垂直向下时的真实RX, RY, RZ值
"""

import sys
import os
import time

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

# 坐标系类型常量
COORD_JOINT = 0      # 关节坐标
COORD_CARTESIAN = 1  # 直角坐标
COORD_TOOL = 2       # 工具坐标
COORD_USER = 3       # 用户坐标

def get_current_cartesian_pose(socket_fd):
    """获取当前笛卡尔位姿"""
    try:
        # 创建位置向量
        pos = nrc.VectorDouble()
        for i in range(6):
            pos.append(0.0)
        
        # 调用函数获取当前位置（使用直角坐标系）
        result = nrc.get_current_position(socket_fd, COORD_CARTESIAN, pos)
        print(f"函数返回值: {result}")
        
        if result == 0:  # 0表示成功
            # 从VectorDouble中提取数据
            current_pose = [pos[i] for i in range(6)]
            return current_pose  # [X, Y, Z, RX, RY, RZ]
        else:
            print(f"获取位置失败，错误码: {result}")
            return None
            
    except Exception as e:
        print(f"❌ 获取当前位姿失败: {e}")
        return None

def main():
    print("=" * 50)
    print("机械臂笛卡尔姿态读取测试")
    print("=" * 50)
    
    socket_fd = -1
    try:
        # 连接机械臂
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd <= 0:
            print("❌ 连接失败！")
            return
        print(f"✅ 连接成功！Socket ID: {socket_fd}")
        
        # 读取当前位姿
        print("\n📍 读取当前笛卡尔位姿...")
        current_pose = get_current_cartesian_pose(socket_fd)
        
        if current_pose:
            x, y, z, rx, ry, rz = current_pose
            print(f"✅ 当前笛卡尔位姿:")
            print(f"  位置: X={x:.6f}, Y={y:.6f}, Z={z:.6f}")
            print(f"  姿态: RX={rx:.6f}, RY={ry:.6f}, RZ={rz:.6f}")
            
            print(f"\n📋 复制以下代码到你的程序中:")
            print(f"DEFAULT_VERTICAL_POSE = {{")
            print(f"    'rx': {rx:.6f},")
            print(f"    'ry': {ry:.6f},")
            print(f"    'rz': {rz:.6f}")
            print(f"}}")
        else:
            print("❌ 无法读取当前位姿")
            
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    finally:
        if socket_fd > 0:
            print("\n🔌 断开连接...")
            nrc.disconnect_robot(socket_fd)
            print("✅ 连接已断开")

if __name__ == "__main__":
    main()
